#include <gtest/gtest.h>
#include "atom/meta/constructor.hpp"
#include "atom/meta/bind_first.hpp"
#include "atom/meta/invoke.hpp"
#include "atom/error/exception.hpp"

#include <functional>
#include <string>

namespace {

// Test classes for constructor tests
class SimpleClass {
private:
    int value_;
    std::string name_;

public:
    SimpleClass() : value_(0), name_("Default") {}
    SimpleClass(int value) : value_(value), name_("FromInt") {}
    SimpleClass(int value, std::string name)
        : value_(value), name_(std::move(name)) {}

    int getValue() const { return value_; }
    const std::string& getName() const { return name_; }
    void setValue(int value) { value_ = value; }
    void setName(const std::string& name) { name_ = name; }

    bool operator==(const SimpleClass& other) const {
        return value_ == other.value_ && name_ == other.name_;
    }
};

// Free functions for testing
int add(int a, int b) { return a + b; }
int multiply(int a, int b, int c) { return a * b * c; }
std::string concatenate(const std::string& a, const std::string& b) {
    return a + b;
}
void incrementCounter(int& counter) { counter++; }

}  // anonymous namespace

// Test fixture for constructor tests
class ConstructorTest : public ::testing::Test {
protected:
    void SetUp() override {}
    void TearDown() override {}
};

// Test basic constructor functionality
TEST_F(ConstructorTest, BasicConstructors) {
    // Default constructor
    auto defaultCtor = atom::meta::buildDefaultConstructor<SimpleClass>();
    auto instance = defaultCtor();
    EXPECT_EQ(instance.getValue(), 0);
    EXPECT_EQ(instance.getName(), "Default");

    // Parameterized constructor
    auto paramCtor = [](int value, const std::string& name) {
        return SimpleClass(value, name);
    };
    auto instance2 = paramCtor(42, "Test");
    EXPECT_EQ(instance2.getValue(), 42);
    EXPECT_EQ(instance2.getName(), "Test");
}

// Test shared pointer constructors
TEST_F(ConstructorTest, SharedConstructors) {
    using namespace atom::meta;

    // Build shared constructor
    auto sharedCtor = buildConstructor<SimpleClass, int, std::string>();
    auto instance = sharedCtor(42, "SharedTest");
    EXPECT_EQ(instance->getValue(), 42);
    EXPECT_EQ(instance->getName(), "SharedTest");

    // Test constructor template
    auto genericCtor = constructor<SimpleClass, int, std::string>();
    auto instance2 = genericCtor(100, "GenericTest");
    EXPECT_EQ(instance2->getValue(), 100);
    EXPECT_EQ(instance2->getName(), "GenericTest");
}

// Test factory constructors
TEST_F(ConstructorTest, FactoryConstructors) {
    using namespace atom::meta;

    auto factory = factoryConstructor<SimpleClass>();

    // Default constructor
    auto instance1 = factory();
    EXPECT_EQ(instance1->getValue(), 0);
    EXPECT_EQ(instance1->getName(), "Default");

    // Single parameter constructor
    auto instance2 = factory(42);
    EXPECT_EQ(instance2->getValue(), 42);
    EXPECT_EQ(instance2->getName(), "FromInt");

    // Two parameter constructor
    auto instance3 = factory(100, "FactoryTest");
    EXPECT_EQ(instance3->getValue(), 100);
    EXPECT_EQ(instance3->getName(), "FactoryTest");
}

// Test fixture for bind_first tests
class BindFirstTest : public ::testing::Test {
protected:
    void SetUp() override {}
    void TearDown() override {}
};

// Test basic bindFirst functionality with free functions
TEST_F(BindFirstTest, BasicBindFirst) {
    // Test with simple addition function
    auto addFive = atom::meta::bindFirst(add, 5);
    EXPECT_EQ(addFive(3), 8);
    EXPECT_EQ(addFive(10), 15);

    // Test with string concatenation
    auto prefixHello = atom::meta::bindFirst(concatenate, std::string("Hello, "));
    EXPECT_EQ(prefixHello("World"), "Hello, World");
    EXPECT_EQ(prefixHello("Test"), "Hello, Test");
}

// Test bindFirst with multiple parameters
TEST_F(BindFirstTest, MultipleParameters) {
    // Bind first parameter of three-parameter function
    auto multiplyByTwo = atom::meta::bindFirst(multiply, 2);
    EXPECT_EQ(multiplyByTwo(3, 4), 24);  // 2 * 3 * 4 = 24
    EXPECT_EQ(multiplyByTwo(5, 6), 60);  // 2 * 5 * 6 = 60
}

// Test bindFirst with references
TEST_F(BindFirstTest, References) {
    int counter = 0;
    auto boundIncrement = atom::meta::bindFirst(incrementCounter, std::ref(counter));

    boundIncrement();
    EXPECT_EQ(counter, 1);

    boundIncrement();
    EXPECT_EQ(counter, 2);
}

// Test bindFirst with std::function
TEST_F(BindFirstTest, StdFunction) {
    std::function<int(int, int)> func = add;
    auto boundFunc = atom::meta::bindFirst(func, 7);

    EXPECT_EQ(boundFunc(3), 10);  // 7 + 3 = 10
}

// Test fixture for invoke tests
class InvokeTest : public ::testing::Test {
protected:
    void SetUp() override {}
    void TearDown() override {}
};

// Test validate_then_invoke
TEST_F(InvokeTest, ValidateThenInvoke) {
    auto isPositive = [](int a, int b) { return a > 0 && b > 0; };
    auto validateAddPositive = atom::meta::validate_then_invoke(isPositive, add);

    // Test with valid inputs
    EXPECT_EQ(validateAddPositive(5, 3), 8);

    // Test with invalid inputs
    EXPECT_THROW(validateAddPositive(-5, 3), atom::error::InvalidArgument);
    EXPECT_THROW(validateAddPositive(5, -3), atom::error::InvalidArgument);
}




