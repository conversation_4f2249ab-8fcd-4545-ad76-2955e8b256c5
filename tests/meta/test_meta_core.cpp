#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include "atom/meta/any.hpp"
#include "atom/meta/anymeta.hpp"

#include <string>
#include <vector>
#include <memory>

using namespace atom::meta;
using ::testing::HasSubstr;

// Test fixture for BoxedValue tests
class BoxedValueTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Initialize test values
        intValue = 42;
        doubleValue = 3.14159;
        stringValue = "Hello, BoxedValue!";
        boolValue = true;
    }

    // Test values
    int intValue;
    double doubleValue;
    std::string stringValue;
    bool boolValue;

    // Helper struct for testing
    struct TestStruct {
        int id;
        std::string name;
        
        TestStruct(int i, std::string n) : id(i), name(std::move(n)) {}
        
        bool operator==(const TestStruct& other) const {
            return id == other.id && name == other.name;
        }
    };
};

// Test basic construction and type checking
TEST_F(BoxedValueTest, BasicConstruction) {
    // Test default constructor (creates undefined value)
    BoxedValue voidVal;
    EXPECT_TRUE(voidVal.isUndef());
    EXPECT_TRUE(voidVal.isNull());

    // Test construction with various types
    BoxedValue intVal(intValue);
    EXPECT_TRUE(intVal.isType<int>());
    EXPECT_FALSE(intVal.isVoid());
    EXPECT_FALSE(intVal.isUndef());

    BoxedValue doubleVal(doubleValue);
    EXPECT_TRUE(doubleVal.isType<double>());

    BoxedValue stringVal(stringValue);
    EXPECT_TRUE(stringVal.isType<std::string>());

    BoxedValue boolVal(boolValue);
    EXPECT_TRUE(boolVal.isType<bool>());

    // Test with custom struct
    TestStruct testStruct(1, "test");
    BoxedValue structVal(testStruct);
    EXPECT_TRUE(structVal.isType<TestStruct>());
}

// Test value retrieval and casting
TEST_F(BoxedValueTest, ValueRetrievalAndCasting) {
    BoxedValue intVal(intValue);
    BoxedValue stringVal(stringValue);
    BoxedValue structVal(TestStruct(2, "struct_test"));

    // Test successful casting
    auto int_opt = intVal.tryCast<int>();
    EXPECT_TRUE(int_opt.has_value());
    EXPECT_EQ(*int_opt, intValue);

    auto string_opt = stringVal.tryCast<std::string>();
    EXPECT_TRUE(string_opt.has_value());
    EXPECT_EQ(*string_opt, stringValue);

    auto struct_opt = structVal.tryCast<TestStruct>();
    EXPECT_TRUE(struct_opt.has_value());
    EXPECT_EQ(struct_opt->id, 2);
    EXPECT_EQ(struct_opt->name, "struct_test");

    // Test failed casting (should return nullopt)
    auto failed_cast1 = intVal.tryCast<std::string>();
    EXPECT_FALSE(failed_cast1.has_value());

    auto failed_cast2 = stringVal.tryCast<int>();
    EXPECT_FALSE(failed_cast2.has_value());
}

// Test copy and move semantics
TEST_F(BoxedValueTest, CopyAndMoveSemantics) {
    BoxedValue original(stringValue);

    // Test copy constructor
    BoxedValue copied(original);
    EXPECT_TRUE(copied.isType<std::string>());
    auto copied_opt = copied.tryCast<std::string>();
    EXPECT_TRUE(copied_opt.has_value());
    EXPECT_EQ(*copied_opt, stringValue);

    auto original_opt = original.tryCast<std::string>();
    EXPECT_TRUE(original_opt.has_value());
    EXPECT_EQ(*original_opt, stringValue); // Original unchanged

    // Test copy assignment
    BoxedValue assigned;
    assigned = original;
    EXPECT_TRUE(assigned.isType<std::string>());
    auto assigned_opt = assigned.tryCast<std::string>();
    EXPECT_TRUE(assigned_opt.has_value());
    EXPECT_EQ(*assigned_opt, stringValue);

    // Test move constructor
    BoxedValue moved(std::move(copied));
    EXPECT_TRUE(moved.isType<std::string>());
    auto moved_opt = moved.tryCast<std::string>();
    EXPECT_TRUE(moved_opt.has_value());
    EXPECT_EQ(*moved_opt, stringValue);

    // Test move assignment
    BoxedValue moveAssigned;
    moveAssigned = std::move(assigned);
    EXPECT_TRUE(moveAssigned.isType<std::string>());
    auto move_assigned_opt = moveAssigned.tryCast<std::string>();
    EXPECT_TRUE(move_assigned_opt.has_value());
    EXPECT_EQ(*move_assigned_opt, stringValue);
}

// Test basic functionality
TEST_F(BoxedValueTest, BasicFunctionality) {
    // Test that BoxedValue can hold different types
    BoxedValue intVal(42);
    BoxedValue stringVal(std::string("test"));
    BoxedValue doubleVal(3.14);

    // Test type checking
    EXPECT_TRUE(intVal.isType<int>());
    EXPECT_FALSE(intVal.isType<std::string>());

    EXPECT_TRUE(stringVal.isType<std::string>());
    EXPECT_FALSE(stringVal.isType<int>());

    EXPECT_TRUE(doubleVal.isType<double>());
    EXPECT_FALSE(doubleVal.isType<int>());
}

// Test attribute system
TEST_F(BoxedValueTest, AttributeSystem) {
    BoxedValue val(intValue);

    // Test setting and getting attributes
    val.setAttr("description", BoxedValue(std::string("Test integer")));
    val.setAttr("category", BoxedValue(std::string("number")));
    val.setAttr("readonly", BoxedValue(false));

    EXPECT_TRUE(val.hasAttr("description"));
    EXPECT_TRUE(val.hasAttr("category"));
    EXPECT_TRUE(val.hasAttr("readonly"));
    EXPECT_FALSE(val.hasAttr("nonexistent"));

    // Test attribute retrieval
    auto desc = val.getAttr("description");
    EXPECT_TRUE(desc.isType<std::string>());
    auto desc_opt = desc.tryCast<std::string>();
    EXPECT_TRUE(desc_opt.has_value());
    EXPECT_EQ(*desc_opt, "Test integer");

    auto readonly = val.getAttr("readonly");
    EXPECT_TRUE(readonly.isType<bool>());
    auto readonly_opt = readonly.tryCast<bool>();
    EXPECT_TRUE(readonly_opt.has_value());
    EXPECT_FALSE(*readonly_opt);

    // Test attribute removal
    val.removeAttr("category");
    EXPECT_FALSE(val.hasAttr("category"));
    EXPECT_TRUE(val.hasAttr("description")); // Others should remain

    // Test getting nonexistent attribute
    auto nonexistent = val.getAttr("nonexistent");
    EXPECT_TRUE(nonexistent.isUndef());
}

// Test type information
TEST_F(BoxedValueTest, TypeInformation) {
    BoxedValue intVal(intValue);
    BoxedValue stringVal(stringValue);
    BoxedValue structVal(TestStruct(3, "type_test"));

    // Test type info access
    const TypeInfo& intTypeInfo = intVal.getTypeInfo();
    EXPECT_EQ(intTypeInfo.name(), "int");
    EXPECT_FALSE(intTypeInfo.isPointer());
    EXPECT_FALSE(intTypeInfo.isReference());

    const TypeInfo& stringTypeInfo = stringVal.getTypeInfo();
    EXPECT_THAT(stringTypeInfo.name(), HasSubstr("string"));

    const TypeInfo& structTypeInfo = structVal.getTypeInfo();
    EXPECT_THAT(structTypeInfo.name(), HasSubstr("TestStruct"));
}

// Test readonly and const behavior
TEST_F(BoxedValueTest, ReadonlyAndConstBehavior) {
    BoxedValue val(intValue);

    // Test readonly status (read-only, no setters available)
    EXPECT_FALSE(val.isReadonly());  // Should be false by default
}

// Test return value flag
TEST_F(BoxedValueTest, ReturnValueFlag) {
    BoxedValue val(intValue);

    // Test return value flag (read-only, no setters available)
    EXPECT_FALSE(val.isReturnValue());  // Should be false by default
}

// Test fixture for TypeMetadata tests
class TypeMetadataTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Create a test class for metadata testing
        metadata = std::make_shared<TypeMetadata>();
    }

    std::shared_ptr<TypeMetadata> metadata;

    // Helper test class
    class TestClass {
    public:
        int value;
        std::string name;
        
        TestClass(int v = 0, std::string n = "") : value(v), name(std::move(n)) {}
        
        int getValue() const { return value; }
        void setValue(int v) { value = v; }
        
        std::string getName() const { return name; }
        void setName(const std::string& n) { name = n; }
        
        int add(int a, int b) { return a + b; }
        std::string concatenate(const std::string& a, const std::string& b) {
            return a + b;
        }
    };
};

// Test basic metadata creation and properties
TEST_F(TypeMetadataTest, BasicMetadataCreation) {
    // TypeMetadata should be created successfully
    EXPECT_NE(metadata, nullptr);

    // Initially should have no methods for a test method name
    EXPECT_EQ(metadata->getMethods("testMethod"), nullptr);
}

// Test method registration and invocation
TEST_F(TypeMetadataTest, MethodRegistration) {
    // Register a simple method
    metadata->addMethod("add", [](std::vector<BoxedValue> args) -> BoxedValue {
        if (args.size() != 2) {
            throw std::invalid_argument("add requires 2 arguments");
        }
        auto a_opt = args[0].tryCast<int>();
        auto b_opt = args[1].tryCast<int>();
        if (!a_opt || !b_opt) {
            throw std::invalid_argument("Arguments must be integers");
        }
        return BoxedValue(*a_opt + *b_opt);
    });

    // Test method existence
    auto methods = metadata->getMethods("add");
    EXPECT_NE(methods, nullptr);
    EXPECT_FALSE(methods->empty());

    // Test that non-existent method returns nullptr
    EXPECT_EQ(metadata->getMethods("nonexistent"), nullptr);

    // Test method invocation through the method vector
    std::vector<BoxedValue> args = {BoxedValue(5), BoxedValue(3)};
    BoxedValue result = (*methods)[0](args);
    auto result_opt = result.tryCast<int>();
    EXPECT_TRUE(result_opt.has_value());
    EXPECT_EQ(*result_opt, 8);

    // Test method with wrong arguments
    std::vector<BoxedValue> wrongArgs = {BoxedValue(5)};
    EXPECT_THROW((*methods)[0](wrongArgs), std::invalid_argument);
}

// Test property registration and access
TEST_F(TypeMetadataTest, PropertyRegistration) {
    // Register a simple property with getter and setter
    metadata->addProperty("testProp",
        [](const BoxedValue& obj) -> BoxedValue {
            // Simple getter that returns a fixed value
            return BoxedValue(42);
        },
        [](BoxedValue& obj, const BoxedValue& value) {
            // Simple setter that does nothing for this test
        }
    );

    // Test that we can add properties without errors
    EXPECT_NO_THROW(metadata->addProperty("anotherProp",
        [](const BoxedValue& obj) -> BoxedValue { return BoxedValue(100); },
        [](BoxedValue& obj, const BoxedValue& value) {}
    ));
}

// Test constructor registration
TEST_F(TypeMetadataTest, ConstructorRegistration) {
    // Register default constructor
    metadata->addConstructor("TestClass", [](std::vector<BoxedValue> args) -> BoxedValue {
        if (args.empty()) {
            return BoxedValue(42);  // Return a simple int for testing
        }
        throw std::invalid_argument("Default constructor takes no arguments");
    });

    // Test that constructor was added without errors
    EXPECT_NO_THROW(metadata->addConstructor("TestClass", [](std::vector<BoxedValue> args) -> BoxedValue {
        return BoxedValue(100);
    }));
}

// Test event system
TEST_F(TypeMetadataTest, EventSystem) {
    bool eventTriggered = false;

    // Register event handler
    metadata->addEventListener("test_event",
        [&eventTriggered](BoxedValue& obj, const std::vector<BoxedValue>& args) {
            eventTriggered = true;
        }
    );

    // Test that event was added without errors
    EXPECT_NO_THROW(metadata->addEvent("another_event", "Test event"));
}

// Test method overloading
TEST_F(TypeMetadataTest, MethodOverloading) {
    // Register overloaded methods with different signatures
    metadata->addMethod("process", [](std::vector<BoxedValue> args) -> BoxedValue {
        if (args.size() == 1 && args[0].isType<int>()) {
            auto val_opt = args[0].tryCast<int>();
            if (val_opt) {
                return BoxedValue(*val_opt * 2);
            }
        }
        throw std::invalid_argument("process(int) signature not matched");
    });

    metadata->addMethod("process", [](std::vector<BoxedValue> args) -> BoxedValue {
        if (args.size() == 1 && args[0].isType<std::string>()) {
            auto val_opt = args[0].tryCast<std::string>();
            if (val_opt) {
                return BoxedValue(*val_opt + "_processed");
            }
        }
        throw std::invalid_argument("process(string) signature not matched");
    });

    // Test that methods were registered
    auto methods = metadata->getMethods("process");
    EXPECT_NE(methods, nullptr);
    EXPECT_EQ(methods->size(), 2);  // Two overloads
}

// Test basic functionality
TEST_F(TypeMetadataTest, BasicFunctionality) {
    // Test that we can create and use TypeMetadata
    EXPECT_NE(metadata, nullptr);

    // Test adding multiple methods
    metadata->addMethod("method1", [](std::vector<BoxedValue> args) -> BoxedValue {
        return BoxedValue(1);
    });

    metadata->addMethod("method2", [](std::vector<BoxedValue> args) -> BoxedValue {
        return BoxedValue(2);
    });

    // Test that methods were added
    EXPECT_NE(metadata->getMethods("method1"), nullptr);
    EXPECT_NE(metadata->getMethods("method2"), nullptr);
    EXPECT_EQ(metadata->getMethods("nonexistent"), nullptr);

    // Test adding properties
    EXPECT_NO_THROW(metadata->addProperty("prop1",
        [](const BoxedValue& obj) -> BoxedValue { return BoxedValue(100); },
        [](BoxedValue& obj, const BoxedValue& value) {}
    ));

    // Test adding constructors
    EXPECT_NO_THROW(metadata->addConstructor("TestType",
        [](std::vector<BoxedValue> args) -> BoxedValue { return BoxedValue(42); }
    ));

    // Test adding events
    EXPECT_NO_THROW(metadata->addEvent("testEvent", "Test event description"));
}
